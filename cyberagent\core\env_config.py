"""
环境配置管理器

这个模块负责加载和管理项目的环境变量配置，
确保配置的统一性和安全性。
"""

import os
from pathlib import Path
from dataclasses import dataclass


def load_env_file(env_file: str = ".env") -> None:
    """加载环境变量文件"""
    env_path = Path(env_file)
    if not env_path.exists():
        print(f"⚠️  环境配置文件 {env_file} 不存在")
        print(f"💡 请复制 .env.template 为 {env_file} 并配置您的环境")
        return

    try:
        with open(env_path, "r", encoding="utf-8") as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()

                # 跳过空行和注释
                if not line or line.startswith("#"):
                    continue

                # 解析键值对
                if "=" in line:
                    key, value = line.split("=", 1)
                    key = key.strip()
                    value = value.strip()

                    # 移除引号
                    if value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    elif value.startswith("'") and value.endswith("'"):
                        value = value[1:-1]

                    # 设置环境变量（.env文件优先级高于系统环境变量）
                    os.environ[key] = value
                else:
                    print(f"⚠️  环境配置文件第 {line_num} 行格式错误: {line}")

    except Exception as e:
        print(f"❌ 加载环境配置文件失败: {e}")


def get_env_bool(key: str, default: bool = False) -> bool:
    """获取布尔类型的环境变量"""
    value = os.getenv(key, str(default)).lower()
    return value in ("true", "1", "yes", "on")


def get_env_int(key: str, default: int = 0) -> int:
    """获取整数类型的环境变量"""
    try:
        return int(os.getenv(key, str(default)))
    except ValueError:
        return default


@dataclass
class ServerConfig:
    """Web服务器配置"""

    host: str
    port: int
    auto_open_browser: bool

    @classmethod
    def from_env(cls) -> "ServerConfig":
        return cls(
            host=os.getenv("WEB_HOST", "127.0.0.1"),
            port=get_env_int("WEB_PORT", 8001),
            auto_open_browser=get_env_bool("AUTO_OPEN_BROWSER", True),
        )


@dataclass
class ModelConfig:
    """LLM模型配置"""

    ollama_host: str
    ollama_port: int
    ollama_base_url: str
    default_model: str
    use_mock: bool
    timeout: int

    @classmethod
    def from_env(cls) -> "ModelConfig":
        ollama_host = os.getenv("OLLAMA_HOST", "localhost")
        ollama_port = get_env_int("OLLAMA_PORT", 11434)

        # 构建base_url
        base_url = f"http://{ollama_host}:{ollama_port}"

        return cls(
            ollama_host=ollama_host,
            ollama_port=ollama_port,
            ollama_base_url=base_url,
            default_model=os.getenv("DEFAULT_MODEL", "qwen3:32b"),
            use_mock=get_env_bool("USE_MOCK_MODEL", False),
            timeout=get_env_int("MODEL_TIMEOUT", 60),
        )


@dataclass
class LogConfig:
    """日志配置"""

    root_dir: str
    level: str

    @classmethod
    def from_env(cls) -> "LogConfig":
        return cls(
            root_dir=os.getenv("LOG_ROOT_DIR", "logs"),
            level=os.getenv("LOG_LEVEL", "INFO"),
        )


class EnvConfigManager:
    """环境配置管理器"""

    def __init__(self, env_file: str = ".env"):
        # 加载环境变量文件
        load_env_file(env_file)

        # 初始化各个配置
        self.server = ServerConfig.from_env()
        self.model = ModelConfig.from_env()
        self.log = LogConfig.from_env()

    def validate_config(self) -> bool:
        """验证配置的有效性"""
        errors = []

        # 验证端口范围
        if not (1 <= self.server.port <= 65535):
            errors.append(f"无效的端口号: {self.server.port}")

        # 验证日志级别
        valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.log.level.upper() not in valid_log_levels:
            errors.append(f"无效的日志级别: {self.log.level}")

        if errors:
            print("❌ 配置验证失败:")
            for error in errors:
                print(f"  - {error}")
            return False

        return True

    def print_config_summary(self) -> None:
        """打印配置摘要"""
        print("📋 当前配置摘要:")
        print(f"  🌐 服务器: {self.server.host}:{self.server.port}")
        print(f"  🤖 模型服务: {self.model.ollama_base_url}")
        print(f"  🎯 默认模型: {self.model.default_model}")
        print(f"  📁 日志目录: {self.log.root_dir}")
        print(f"  📊 日志级别: {self.log.level}")


# 全局配置实例
_config_manager = None


def get_config() -> EnvConfigManager:
    """获取全局配置管理器"""
    global _config_manager
    if _config_manager is None:
        _config_manager = EnvConfigManager()
    return _config_manager


def reload_config(env_file: str = ".env") -> EnvConfigManager:
    """重新加载配置"""
    global _config_manager
    _config_manager = EnvConfigManager(env_file)
    return _config_manager
