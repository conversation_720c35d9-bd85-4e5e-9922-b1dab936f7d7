/* iOS风格的现代化样式 */
:root {
    /* iOS风格配色 */
    --primary-color: #007AFF;
    --secondary-color: #5856D6;
    --success-color: #34C759;
    --warning-color: #FF9500;
    --error-color: #FF3B30;
    --background-color: #F2F2F7;
    --surface-color: #FFFFFF;
    --text-primary: #000000;
    --text-secondary: #8E8E93;
    --border-color: #C6C6C8;
    --shadow-color: rgba(0, 0, 0, 0.1);

    /* iOS风格间距系统 - 8px基础网格 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 40px;

    /* 圆角 */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;

    /* 字体 */
    --font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

.app-container {
    min-height: 100vh;
    display: flex;
    background: var(--background-color);
}

/* 侧边栏样式 */
.sidebar {
    width: 280px;
    background: var(--surface-color);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 100;
}

.sidebar-header {
    padding: var(--spacing-lg); /* 统一内边距 */
    border-bottom: 1px solid var(--border-color);
}

.sidebar-footer {
    padding: var(--spacing-md);
    border-top: 1px solid var(--border-color);
    margin-top: auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm); /* 减小图标和文字的间距 */
    margin-bottom: var(--spacing-xl); /* 增加与下方按钮的间距 */
}

.logo i {
    font-size: 1.5rem; /* 保持图标大小 */
    color: var(--primary-color);
    animation: pulse 2s infinite;
}

.logo h2 {
    font-size: 1.2rem; /* 微调标题字体大小 */
    font-weight: 600;
    color: var(--text-primary);
}

/* 新对话按钮 */
.new-chat-btn {
    width: 100%;
    margin-bottom: 0; /* 移除按钮下方的间距，由 sidebar-header 控制 */
    padding: 12px var(--spacing-md); /* 调整按钮内边距 */
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-family: var(--font-family);
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    min-height: 44px;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
}

.new-chat-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 122, 255, 0.3);
}

/* 会话列表 */
.sessions-list {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md);
}

.session-item {
    padding: 12px 16px;
    margin-bottom: 6px;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    background: var(--surface-color);
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.session-item:hover {
    background: var(--background-color);
    border-color: var(--border-color);
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
}

.session-item.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    box-shadow: 0 2px 12px rgba(0, 122, 255, 0.25);
}

.session-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 8px;
}

.session-title {
    font-weight: 600;
    font-size: 0.875rem;
    line-height: 1.2;
    flex: 1;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-word;
}

.session-time {
    font-size: 0.7rem;
    opacity: 0.6;
    white-space: nowrap;
    flex-shrink: 0;
    margin-top: 1px;
}

.session-preview {
    font-size: 0.75rem;
    opacity: 0.7;
    line-height: 1.3;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    word-break: break-word;
}

/* 模型选择器 */
.model-selector {
    padding: var(--spacing-md);
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
}

.model-selector label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.model-select {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--surface-color);
    color: var(--text-primary);
    font-family: var(--font-family);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.model-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.model-select:hover {
    border-color: var(--primary-color);
}

.model-select option {
    padding: var(--spacing-sm);
    background: var(--surface-color);
    color: var(--text-primary);
}

.status-indicators {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

/* 聊天容器 */
.chat-container {
    flex: 1;
    margin-left: 280px;
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: var(--surface-color);
}

/* 聊天头部 */
.chat-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--surface-color);
}

.chat-title h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.chat-subtitle {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.chat-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.action-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--background-color);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
}

.action-btn:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

/* 聊天消息区域 */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* 欢迎消息 */
.welcome-message {
    text-align: center;
    max-width: 600px;
    margin: auto;
    padding: var(--spacing-xl);
}

.welcome-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    color: white;
    font-size: 2rem;
}

.welcome-message h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.welcome-message p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

/* 聊天消息样式 */
.message {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    animation: slideIn 0.3s ease;
    align-items: flex-start;
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    flex-shrink: 0;
    margin-top: var(--spacing-xs);
    position: relative;
}

.message-avatar i {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.message-avatar .model-name {
    font-size: 0.65rem;
    color: var(--text-secondary);
    margin-top: 4px;
    text-align: center;
    line-height: 1.1;
    max-width: 50px;
    word-break: break-word;
    font-weight: 500;
    opacity: 0.8;
}

.message.user .message-avatar i {
    background: var(--primary-color);
    color: white;
}

.message.assistant .message-avatar i {
    background: var(--background-color);
    color: var(--text-secondary);
    border: 2px solid var(--border-color);
}

.message.tool-execution .message-avatar i {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.message.thinking .message-avatar i {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.message-content {
    flex: 1;
    max-width: 75%;
    min-width: 0;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.message.user .message-content {
    text-align: right;
    max-width: 60%; /* 用户消息更窄一些 */
}

.message.assistant .message-content,
.message.tool-execution .message-content,
.message.thinking .message-content {
    max-width: 70%; /* 减少助手消息的最大宽度，防止挤压界面 */
}

.message-bubble {
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    line-height: 1.6;
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
    position: relative;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    display: block; /* 改为block以确保正确的宽度控制 */
    max-width: 100%;
    width: 100%; /* 确保占满容器宽度 */
}

.message.user .message-bubble {
    background: var(--primary-color);
    color: white;
    border-bottom-right-radius: var(--radius-sm);
    /* 用户消息根据内容自适应宽度 */
    width: fit-content;
    min-width: 60px;
    margin-left: auto;
}

.message.assistant .message-bubble {
    background: var(--surface-color);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-bottom-left-radius: var(--radius-sm);
}

/* 工具执行气泡样式 */
.tool-execution-bubble {
    background: linear-gradient(135deg, rgba(240, 147, 251, 0.1) 0%, rgba(245, 87, 108, 0.1) 100%);
    border: 1px solid rgba(240, 147, 251, 0.3);
    border-bottom-left-radius: var(--radius-sm);
}

.tool-execution-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    color: #f5576c;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid rgba(245, 87, 108, 0.2);
}

/* 思维过程气泡样式 */
.thinking-bubble {
    background: linear-gradient(135deg, rgba(79, 172, 254, 0.1) 0%, rgba(0, 242, 254, 0.1) 100%);
    border: 1px solid rgba(79, 172, 254, 0.3);
    border-bottom-left-radius: var(--radius-sm);
}

.thinking-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    color: #4facfe;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid rgba(79, 172, 254, 0.2);
}

/* 系统消息样式 */
.message.system {
    justify-content: center;
    margin: var(--spacing-sm) 0;
}

.message.system .message-content {
    max-width: 80%;
    text-align: center;
}

.system-bubble {
    background: rgba(142, 142, 147, 0.1);
    border: 1px solid rgba(142, 142, 147, 0.3);
    color: var(--text-secondary);
    font-size: 0.875rem;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.system-bubble i {
    color: var(--text-secondary);
    opacity: 0.7;
}

/* 折叠区域样式 - 优化版本 */
.thinking-section,
.tools-section {
    margin: var(--spacing-md) 0;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    overflow: hidden;
    transition: all 0.3s ease;
}

/* REMOVE erroneous duplicate and specific border for .thinking-section */
/* .thinking-section { */
/*     border-left: 3px solid var(--primary-color); */
/* } */

.thinking-section.hidden,
.tools-section.hidden {
    display: none;
}

.section-header {
    padding: var(--spacing-md);
    background: var(--background-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
    transition: all 0.3s ease;
    user-select: none;
}

.section-header:hover {
    background: var(--border-color);
}

.section-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.section-header i.toggle-icon {
    transition: transform 0.3s ease;
    color: var(--text-secondary);
}

.thinking-section.collapsed .toggle-icon,
.tools-section.collapsed .toggle-icon {
    transform: rotate(-90deg);
}

.section-content {
    padding: var(--spacing-md);
    background: var(--surface-color); /* 还原为白色背景 */
    transition: all 0.3s ease;
    max-height: none; /* 移除高度限制，允许完整显示内容 */
    overflow: visible; /* 允许内容完全显示 */
}

.thinking-section.collapsed .section-content,
.tools-section.collapsed .section-content {
    max-height: 0;
    padding: 0 var(--spacing-md);
    opacity: 0;
}

/* Styles for LLM <think> content section - 与其他折叠区域区别化 */
.llm-thinking-section {
    margin: var(--spacing-sm) 0 var(--spacing-sm) 0; /* 减少上下边距，更贴近对话 */
    border: 1px solid rgba(0, 122, 255, 0.3); /* 蓝色边框 */
    border-radius: var(--radius-md);
    overflow: hidden;
    transition: all 0.3s ease;
    border-left: 4px solid var(--primary-color); /* 更粗的蓝色左边框 */
    box-shadow: 0 1px 3px rgba(0, 122, 255, 0.1); /* 轻微蓝色阴影 */
}

.llm-thinking-section .section-header {
    background: rgba(0, 122, 255, 0.08); /* 轻微蓝色背景 */
    border-bottom: 1px solid rgba(0, 122, 255, 0.15);
}

.llm-thinking-section .section-header:hover {
    background: rgba(0, 122, 255, 0.12); /* 悬停时稍深的蓝色 */
}

.llm-thinking-section .section-title i {
    color: var(--primary-color); /* 图标使用主色调 */
}

.llm-thinking-section .section-header {
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    user-select: none;
    transition: background-color 0.2s ease;
}

.llm-thinking-section .toggle-icon {
    transition: transform 0.3s ease;
    color: var(--text-secondary);
    font-size: 0.8em;
}

.llm-thinking-section.collapsed .toggle-icon {
    transform: rotate(-90deg);
}

.llm-thinking-section .section-content {
    padding: var(--spacing-md);
    background: rgba(0, 122, 255, 0.03); /* 非常淡的蓝色背景 */
    transition: all 0.3s ease;
    max-height: none; /* 默认展开，不限制高度 */
    opacity: 1;
    overflow: visible;
}

.llm-thinking-section.collapsed .section-content {
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
    opacity: 0;
    overflow: hidden;
}

/* LLM思考步骤样式 */
.llm-thought-step {
    padding: var(--spacing-sm);
    margin: var(--spacing-sm) 0;
    background: rgba(255, 255, 255, 0.7);
    border-radius: var(--radius-sm);
    border-left: 2px solid rgba(0, 122, 255, 0.3);
    font-size: 0.9rem;
    line-height: 1.5;
}

.llm-thinking-section .llm-thought-step { /* Corrected selector */
    color: var(--text-primary); /* Ensure text is readable on blue background */
    padding: var(--spacing-sm); /* Add padding for each step */
    border-bottom: 1px solid rgba(0, 122, 255, 0.15); /* Separator line */
    line-height: 1.6;
}

.llm-thinking-section .llm-thought-step:last-child {
    border-bottom: none;
}


.thinking-step {
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid rgba(79, 172, 254, 0.1);
    color: var(--text-primary);
    line-height: 1.6;
}

.thinking-step:last-child {
    border-bottom: none;
}

.main-response { /* 这个类用于包裹最终的回复内容 */
    margin-top: var(--spacing-md); /* 与上方的思考/工具部分隔开 */
    padding: var(--spacing-md);
    background: var(--surface-color); /* 确保是白色背景 */
    border-radius: var(--radius-md);
    /* border-left: 3px solid var(--primary-color); */ /* 可以移除或保留，根据设计决定 */
}

/* 如果JS中使用了 assistant-final-response 作为最终回复的容器 */
.assistant-final-response {
    padding: var(--spacing-sm) 0; /* 根据需要调整内边距 */
    /* background: var(--surface-color); /* 确保白色背景 */
    /* color: var(--text-primary); */
}

.assistant-thinking-block {
    background-color: rgba(0, 122, 255, 0.05); /* 淡蓝色背景 */
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-sm); /* 与最终回复的间距 */
}

.assistant-thinking-block .thinking-step {
    color: var(--text-primary); /* 确保文字颜色在蓝色背景上清晰 */
    border-bottom-color: rgba(0, 122, 255, 0.15); /* 调整分隔线颜色 */
}


.loading-dots {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    font-style: italic;
}

.loading-dots::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.message-time {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
    opacity: 0.7;
}

/* 工具执行详情 */
.tool-execution { /* This class will be the main container for each tool execution block within the message */
    margin-bottom: var(--spacing-md); /* Add space between multiple tool executions */
}

/* Styles for the content that was originally in .tool-header, now part of .tool-execution-main-header */
.tool-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-grow: 1; /* Allows it to take available space next to the toggle icon */
}

.tool-header-content .tool-name {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: #f5576c; /* Retain original color for distinction */
    font-weight: 600;
}

.tool-header-content .tool-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    margin-left: var(--spacing-md); /* Space from the toggle icon if on the same line */
}

/* Specific status colors, assuming .tool-status is within .tool-header-content */
.tool-header-content .tool-status.executing {
    background: var(--warning-color);
    color: white;
}

.tool-header-content .tool-status.completed {
    background: var(--success-color);
    color: white;
}

.tool-header-content .tool-status.failed {
    background: var(--error-color);
    color: white;
}

/* .tool-details can be removed if its content (params) is directly in .tool-collapsible-content */
/* If .tool-details is kept as a wrapper for params, style it appropriately */
.tool-details {
    /* margin-top: var(--spacing-sm); */ /* Margin might be handled by collapsible content padding */
}

.tool-params {
    background: rgba(245, 87, 108, 0.05); /* Light red/pinkish background for params */
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 0.875rem;
    overflow-x: auto;
    border: 1px solid rgba(245, 87, 108, 0.1);
    /* margin: 0; */ /* Remove margin if inside .tool-collapsible-content with padding */
}

.tool-result {
    background: rgba(52, 199, 89, 0.05); /* Light greenish background for results */
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 0.875rem;
    overflow-x: auto;
    border: 1px solid rgba(52, 199, 89, 0.2);
    border-left: 3px solid var(--success-color);
    /* margin: 0; */ /* Remove margin if inside .tool-collapsible-content with padding */
}

.tool-result pre, .tool-params pre {
    margin: 0; /* Reset browser default margin for pre */
    white-space: pre-wrap; /* Allow text to wrap */
    word-break: break-all; /* Break long strings to prevent overflow */
}

.tool-time {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: var(--spacing-sm);
    text-align: right;
}

/* General collapsible styles for tool execution */
.tool-collapsible-section {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-sm);
    overflow: hidden; /* Important for smooth collapse/expand */
}

.tool-collapsible-header {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--background-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
    user-select: none;
    transition: background-color 0.2s ease;
}

.tool-collapsible-header:hover {
    background: var(--border-color);
}

.tool-collapsible-header .toggle-icon {
    transition: transform 0.3s ease;
    color: var(--text-secondary);
    font-size: 0.8em; /* Smaller icon */
}

.tool-collapsible-section.collapsed .tool-collapsible-header .toggle-icon {
    transform: rotate(-90deg);
}

.tool-collapsible-content {
    padding: var(--spacing-md);
    background: var(--surface-color);
    border-top: 1px solid var(--border-color);
    transition: max-height 0.3s ease-out, padding 0.3s ease-out, opacity 0.3s ease-out;
    max-height: none; /* 移除高度限制，允许完整显示长内容 */
    opacity: 1;
    overflow: visible; /* 确保内容完全可见 */
}

.tool-collapsible-section.collapsed .tool-collapsible-content {
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
    opacity: 0;
    overflow: hidden; /* Hide content when collapsed */
}

/* Specific styling for tool name/params section header */
.tool-execution-main-header {
    /* Keep existing styles or add new ones if needed */
    /* Example: remove bottom border if tool-collapsible-section adds one */
    border-bottom: none;
}

/* Specific styling for tool result section header */
.tool-result-header {
    font-weight: normal; /* Less emphasis than main tool header */
    font-size: 0.9em;
}


.tool-action {
    margin-bottom: var(--spacing-sm);
    font-size: 0.875rem;
}

.result-content {
    margin-top: var(--spacing-xs);
    padding: var(--spacing-sm);
    background: rgba(52, 199, 89, 0.05);
    border-radius: var(--radius-sm);
    line-height: 1.6;
    max-height: none; /* 移除高度限制，允许完整显示长内容 */
    overflow: visible; /* 确保内容完全可见 */
    word-wrap: break-word; /* 长单词自动换行 */
    white-space: pre-wrap; /* 保持格式化的同时允许换行 */
}

.result-content strong {
    color: var(--success-color);
}

.result-content code {
    background: rgba(0, 0, 0, 0.1);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 0.875rem;
}

/* 思维过程 */
.thinking-process {
    margin: var(--spacing-sm) 0;
    padding: var(--spacing-md);
    background: rgba(79, 172, 254, 0.05);
    border: 1px solid rgba(79, 172, 254, 0.2);
    border-radius: var(--radius-md);
    border-left: 3px solid #4facfe;
}

.thinking-content {
    color: var(--text-primary);
    font-style: italic;
    line-height: 1.6;
    margin: var(--spacing-sm) 0;
}

/* 聊天输入区域 */
.chat-input-container {
    padding: var(--spacing-md);
    border-top: none; /* 移除顶部的线条 */
    background: var(--surface-color);
}

.chat-input-wrapper {
    display: flex;
    gap: var(--spacing-md);
    align-items: flex-end;
    max-width: 800px;
    margin: 0 auto;
}

#chat-input {
    flex: 1;
    min-height: 20px;
    max-height: 120px;
    padding: 12px var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-family: var(--font-family);
    font-size: 1rem;
    resize: none;
    transition: all 0.3s ease;
    background: var(--surface-color);
    line-height: 1.5;
    overflow-y: auto; /* 允许滚动 */
}

/* 自定义滚动条样式 */
#chat-input::-webkit-scrollbar {
    width: 6px;
}

#chat-input::-webkit-scrollbar-track {
    background: transparent;
}

#chat-input::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

#chat-input::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

#chat-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.send-btn {
    width: 48px;
    height: 48px;
    border: none;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
    background: var(--secondary-color);
    transform: scale(1.05);
}

.send-btn:disabled {
    background: var(--border-color);
    cursor: not-allowed;
    transform: none;
}

.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-sm);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    height: 48px;
}

.input-hint {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* 打字指示器 */
.typing-indicator {
    display: none;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.typing-indicator.show {
    display: flex;
}

.typing-dots {
    display: flex;
    gap: 2px;
}

.typing-dots span {
    width: 4px;
    height: 4px;
    background: var(--text-secondary);
    border-radius: 50%;
    animation: typingDots 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes typingDots {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.status-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: 12px var(--spacing-md);
    background: var(--background-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    height: 48px;
    box-sizing: border-box;
}

.status-item:hover {
    background: var(--border-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-dot.active {
    background-color: var(--success-color);
}

.status-dot.connecting {
    background-color: var(--warning-color);
}

.status-dot.error {
    background-color: var(--error-color);
}

/* 状态悬停提示框 */
.status-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--text-primary);
    color: var(--surface-color);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    white-space: pre-line;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    margin-bottom: var(--spacing-xs);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    max-width: 200px;
    text-align: left;
}

.status-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--text-primary);
}

.status-item:hover .status-tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-4px);
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* 卡片样式 */
.card {
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    box-shadow: 0 2px 10px var(--shadow-color);
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px var(--shadow-color);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--surface-color) 0%, var(--background-color) 100%);
}

.card-header.collapsible {
    cursor: pointer;
    user-select: none;
}

.card-header.collapsible:hover {
    background: var(--background-color);
}

.card-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.card-header i {
    color: var(--primary-color);
}

.toggle-icon {
    transition: transform 0.3s ease;
    color: var(--text-secondary);
}

.toggle-icon.rotated {
    transform: rotate(180deg);
}

.card-content {
    padding: var(--spacing-lg);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card-content.collapsed {
    max-height: 0;
    padding: 0 var(--spacing-lg);
    opacity: 0;
}

/* 任务输入区域 */
.input-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

#task-input {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-family: var(--font-family);
    font-size: 1rem;
    resize: vertical;
    transition: all 0.3s ease;
    background: var(--surface-color);
}

#task-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.primary-button {
    align-self: flex-end;
    padding: var(--spacing-md) var(--spacing-xl);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-family: var(--font-family);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.primary-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 122, 255, 0.3);
}

.primary-button:active {
    transform: translateY(0);
}

.primary-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 示例任务样式 */
.example-tasks {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    align-items: center;
    margin: var(--spacing-md) 0;
    padding: var(--spacing-md);
    background: var(--background-color);
    border-radius: var(--radius-md);
}

.example-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin-right: var(--spacing-sm);
}

.example-task {
    padding: var(--spacing-xs) var(--spacing-md);
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.example-task:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
}

.example-task:active {
    transform: translateY(0);
}

/* 规划区域样式 */
.planning-section {
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--surface-color);
    overflow: hidden;
}

.planning-section.collapsed .section-content {
    display: none;
}

.planning-section .section-header {
    padding: var(--spacing-md);
    background: var(--background-color);
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    user-select: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.planning-section .section-header:hover {
    background: var(--border-color);
}

.planning-section .section-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.planning-section .section-title i {
    color: var(--primary-color);
}

.planning-section .toggle-icon {
    transition: transform 0.3s ease;
    color: var(--text-secondary);
}

.planning-section.collapsed .toggle-icon {
    transform: rotate(-90deg);
}

.planning-section .section-content {
    padding: var(--spacing-md);
}

.planning-steps {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.planning-step {
    padding: var(--spacing-md);
    background: var(--background-color);
    border-radius: var(--radius-sm);
    border-left: 4px solid var(--primary-color);
}

.planning-step .step-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
}

.planning-step .step-number {
    background: var(--primary-color);
    color: white;
    padding: 2px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
}

.planning-step .step-tool {
    background: var(--surface-color);
    color: var(--text-primary);
    padding: 2px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    border: 1px solid var(--border-color);
}

.planning-step .step-status {
    padding: 2px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.planning-step .step-status.status-pending {
    background: var(--text-secondary);
    color: white;
}

.planning-step .step-status.status-completed {
    background: var(--success-color);
    color: white;
}

.planning-step .step-description {
    color: var(--text-primary);
    font-size: 0.875rem;
    line-height: 1.4;
}

.planning-step .step-parameters {
    margin-top: var(--spacing-xs);
    padding: var(--spacing-xs);
    background: var(--surface-color);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 规划状态 */
.planning-status {
    margin-bottom: var(--spacing-lg);
}

.status-message {
    padding: var(--spacing-md);
    background: var(--background-color);
    border-radius: var(--radius-md);
    font-weight: 500;
    text-align: center;
    color: var(--text-secondary);
}

/* 步骤容器 */
.steps-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.step-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--background-color);
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
    border-left: 4px solid var(--border-color);
}

.step-item.pending {
    border-left-color: var(--text-secondary);
}

.step-item.in-progress {
    border-left-color: var(--warning-color);
    background: rgba(255, 149, 0, 0.1);
    animation: pulse 2s infinite;
}

.step-item.completed {
    border-left-color: var(--success-color);
    background: rgba(52, 199, 89, 0.1);
}

.step-item.failed {
    border-left-color: var(--error-color);
    background: rgba(255, 59, 48, 0.1);
}

.step-number {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

.step-content {
    flex: 1;
}

.step-title {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.step-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.step-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.step-status.pending {
    background: var(--text-secondary);
    color: white;
}

.step-status.in-progress {
    background: var(--warning-color);
    color: white;
}

.step-status.completed {
    background: var(--success-color);
    color: white;
}

.step-status.failed {
    background: var(--error-color);
    color: white;
}

/* 进度条 */
.execution-progress {
    margin-bottom: var(--spacing-lg);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--border-color);
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: var(--radius-sm);
    transition: width 0.5s ease;
    width: 0%;
}

.progress-text {
    text-align: center;
    font-weight: 600;
    color: var(--text-secondary);
}

/* 占位符样式 */
.cot-placeholder,
.tools-placeholder,
.session-placeholder {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: var(--spacing-xl);
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(242, 242, 247, 0.8);
    backdrop-filter: blur(10px);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

/* 动画 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 打字机光标动画 */
@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.typewriter-cursor {
    color: var(--primary-color);
    font-weight: bold;
    animation: blink 1s infinite;
}

/* 平滑动画 */
.smooth-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 防闪烁优化 */
.message-bubble {
    will-change: auto;
    backface-visibility: hidden;
    transform: translateZ(0);
}

/* 任务状态区域样式 */
.task-status-section {
    margin-top: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    overflow: hidden;
    background: var(--surface-color);
}

.task-status-header {
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(var(--primary-color-rgb), 0.1);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.9em;
    font-weight: 500;
    transition: all 0.3s ease;
    user-select: none;
}

.task-status-header:hover {
    background: rgba(var(--primary-color-rgb), 0.15);
}

.task-status-text {
    color: var(--text-primary);
}

.task-status-section.collapsed .toggle-icon {
    transform: rotate(-90deg);
}

.task-status-content {
    padding: var(--spacing-md);
    border-top: 1px solid var(--border-color);
    font-size: 0.9em;
    color: var(--text-secondary);
}

.task-status-section.collapsed .task-status-content {
    display: none;
}

.task-details p {
    margin: 0;
    line-height: 1.5;
}

/* 设置界面样式 */
.settings-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.settings-overlay.show {
    display: flex;
}

.settings-modal {
    background: white;
    border-radius: 16px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: slideUp 0.3s ease;
}

.settings-header {
    padding: 24px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--surface-color);
    color: var(--text-primary);
}

.settings-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: var(--background-color);
    color: var(--text-primary);
}

.settings-content {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
}

.settings-section {
    margin-bottom: 32px;
}

.settings-section:last-child {
    margin-bottom: 0;
}

.settings-section h4 {
    margin: 0 0 16px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 8px;
}

.setting-item {
    margin-bottom: 20px;
}

.setting-item label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-primary);
}

.setting-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s ease;
    background: var(--surface-color);
}

.setting-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.tool-toggles {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.tool-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.tool-toggle:hover {
    background: var(--background-color);
}

.tool-toggle input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.tool-toggle label {
    margin: 0;
    cursor: pointer;
    font-weight: 500;
    flex: 1;
}

.tool-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.tool-name {
    font-weight: 600;
    color: var(--text-primary);
}

.tool-desc {
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-weight: 400;
}

/* MCP服务器配置样式 */
.mcp-servers {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-top: 12px;
}

.mcp-server-item {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 16px;
    background: var(--surface-color);
}

.mcp-server-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.mcp-server-header label {
    flex: 1;
    font-weight: 600;
    margin: 0;
}

.mcp-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.mcp-status.connected {
    background: rgba(52, 199, 89, 0.1); /* 简化为浅绿色 */
    color: var(--success-color);
}

.mcp-status.disconnected {
    background: var(--background-color); /* 简化为浅灰色 */
    color: var(--text-secondary);
}

.mcp-server-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding-left: 30px;
}

.mcp-config {
    display: flex;
    align-items: center;
    gap: 8px;
}

.mcp-config label {
    min-width: 80px;
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.mcp-config input {
    flex: 1;
    padding: 6px 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.85rem;
    background: var(--background-color);
}

.mcp-tools {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.add-mcp-server {
    margin-top: 12px;
    padding: 8px 16px;
    background: var(--background-color); /* 简化为浅色背景 */
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: all 0.2s ease;
}

.add-mcp-server:hover {
    background: var(--border-color);
    border-color: var(--primary-color);
}

/* 范围滑块样式 */
.setting-range {
    width: 100%;
    margin: 8px 0;
}

.range-value {
    display: inline-block;
    margin-left: 8px;
    font-weight: 600;
    color: var(--primary-color);
}

.settings-footer {
    padding: 24px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    background: var(--background-color);
}

.btn-primary, .btn-secondary {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background: var(--primary-color); /* 简化为纯色背景 */
    color: white;
}

.btn-primary:hover {
    background: var(--secondary-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2); /* 简化阴影 */
}

.btn-secondary {
    background: var(--surface-color);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--border-color);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        position: fixed;
        z-index: 1000;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .chat-container {
        margin-left: 0;
    }

    .message-content {
        max-width: 90%;
    }

    .message.user .message-content {
        max-width: 85%;
    }

    .settings-modal {
        width: 95%;
        max-height: 90vh;
    }

    .tool-toggles {
        grid-template-columns: 1fr;
    }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.5s ease;
}

/* 通知系统 */
.notification-container {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: 1001;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.notification {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    color: white;
    font-weight: 500;
    min-width: 300px;
    box-shadow: 0 4px 15px var(--shadow-color);
    animation: slideInRight 0.3s ease;
    position: relative;
    overflow: hidden;
}

.notification.success {
    background: var(--success-color);
}

.notification.error {
    background: var(--error-color);
}

.notification.warning {
    background: var(--warning-color);
}

.notification.info {
    background: var(--primary-color);
}

.notification::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(255, 255, 255, 0.3);
    animation: notificationProgress 3s linear;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes notificationProgress {
    from { width: 100%; }
    to { width: 0%; }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* 打字机光标样式 */
.typewriter-cursor {
    color: var(--primary-color);
    font-weight: normal;
    margin-left: 2px;
}

/* COT思维过程样式 */
.cot-container {
    max-height: 400px;
    overflow-y: auto;
}

.cot-step {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--background-color);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary-color);
}

.cot-step-title {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.cot-step-content {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* 工具调用详情样式 */
.tool-call {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--background-color);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.tool-call-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.tool-name {
    font-weight: 600;
    color: var(--primary-color);
}

.tool-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.tool-parameters {
    background: var(--surface-color);
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    margin: var(--spacing-sm) 0;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 0.875rem;
    overflow-x: auto;
}

.tool-result {
    background: var(--surface-color);
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    margin-top: var(--spacing-sm);
    border-left: 4px solid var(--success-color);
}

/* 会话历史样式 */
.session-message {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    animation: slideIn 0.3s ease;
}

.session-message.user {
    background: var(--primary-color);
    color: white;
    margin-left: var(--spacing-xl);
}

.session-message.assistant {
    background: var(--background-color);
    margin-right: var(--spacing-xl);
}

.message-timestamp {
    font-size: 0.75rem;
    opacity: 0.7;
    margin-bottom: var(--spacing-xs);
}

.message-content {
    line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .chat-container {
        margin-left: 0;
    }

    .chat-header {
        padding: var(--spacing-md);
    }

    .chat-messages {
        padding: var(--spacing-md);
    }

    .chat-input-container {
        padding: var(--spacing-md);
    }

    .message-content {
        max-width: 85%;
    }

    .welcome-message {
        padding: var(--spacing-lg);
    }

    .welcome-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .notification-container {
        left: var(--spacing-md);
        right: var(--spacing-md);
    }

    .notification {
        min-width: auto;
    }

    .example-tasks {
        flex-direction: column;
        align-items: stretch;
    }

    .example-task {
        text-align: center;
    }
}

/* 移动端侧边栏切换按钮 */
.sidebar-toggle {
    display: none;
    position: fixed;
    top: var(--spacing-md);
    left: var(--spacing-md);
    z-index: 101;
    width: 44px;
    height: 44px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(0, 122, 255, 0.3);
}

@media (max-width: 768px) {
    .sidebar-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

/* 流式UI样式 */

/* 规划状态指示器 */
.planning-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-left: var(--spacing-sm);
    padding: 4px 8px;
    background: rgba(0, 122, 255, 0.1);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    animation: pulse 1.5s infinite;
}

.status-indicator.thinking {
    background: var(--warning-color);
}

.status-indicator.analysis {
    background: var(--primary-color);
}

.status-indicator.model_generation {
    background: var(--secondary-color);
}

.status-indicator.steps_generated {
    background: var(--success-color);
}

.status-indicator.executing {
    background: var(--error-color);
    animation: pulse 1s infinite;
}

/* 进度条样式 */
.planning-progress,
.execution-progress {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm);
    background: var(--background-color);
    border-radius: var(--radius-sm);
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: var(--border-color);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 3px;
    transition: width 0.3s ease;
    animation: shimmer 2s infinite;
}

.progress-text {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-secondary);
    min-width: 35px;
    text-align: right;
}

/* 规划思维过程 */
.planning-thoughts {
    margin-top: var(--spacing-md);
    padding: var(--spacing-md);
    background: rgba(0, 122, 255, 0.05);
    border-radius: var(--radius-sm);
    border-left: 3px solid var(--primary-color);
}

.planning-thoughts h4 {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.planning-thought {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid rgba(0, 122, 255, 0.1);
}

.planning-thought:last-child {
    border-bottom: none;
}

.thought-content {
    flex: 1;
    font-size: 0.8rem;
    color: var(--text-primary);
}

.thought-stage {
    font-size: 0.7rem;
    color: var(--text-secondary);
    background: rgba(0, 122, 255, 0.1);
    padding: 2px 6px;
    border-radius: var(--radius-sm);
}

/* 当前执行状态 */
.current-execution {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-left: var(--spacing-sm);
    padding: 4px 8px;
    background: rgba(255, 59, 48, 0.1);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
}

.execution-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--error-color);
    animation: pulse 1s infinite;
}

.execution-text {
    color: var(--error-color);
    font-weight: 500;
}

/* 执行错误 */
.execution-errors {
    margin-top: var(--spacing-md);
    padding: var(--spacing-md);
    background: rgba(255, 59, 48, 0.05);
    border-radius: var(--radius-sm);
    border-left: 3px solid var(--error-color);
}

.execution-errors h4 {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--error-color);
}

.execution-error {
    display: flex;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid rgba(255, 59, 48, 0.1);
}

.execution-error:last-child {
    border-bottom: none;
}

.error-step {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--error-color);
    min-width: 60px;
}

.error-message {
    flex: 1;
    font-size: 0.8rem;
    color: var(--text-primary);
}

/* 动画效果 */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.1);
    }
}

/* 流式更新的平滑过渡 */
.planning-section,
.tools-section {
    transition: all 0.3s ease;
}

.planning-section.hidden,
.tools-section.hidden {
    opacity: 0.5;
    transform: translateY(-5px);
}

/* 实时状态更新 */
.section-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.section-title .planning-status,
.section-title .current-execution {
    margin-left: auto;
    margin-right: 0;
}

/* 规划思维区域样式 */
.planning-thoughts-section {
    margin-bottom: var(--spacing-md);
}

.planning-thoughts-section h4 {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: rgba(0, 122, 255, 0.1);
    border-radius: var(--radius-sm);
    border-left: 3px solid var(--primary-color);
}

.planning-result {
    margin-top: var(--spacing-md);
    padding: var(--spacing-md);
    background: rgba(0, 122, 255, 0.05);
    border-radius: var(--radius-sm);
    border: 1px solid rgba(0, 122, 255, 0.2);
}

.planning-result h5 {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: var(--spacing-sm);
}

/* 执行思维区域样式 */
.execution-thoughts-section {
    margin-top: 0; /* Adjusted to reduce top blank space */
    /* border-top: 1px solid var(--border-color); */ /* Removed as per user request */
    padding-top: var(--spacing-sm); /* Adjusted to reduce top blank space */
}

.execution-thoughts-section h4 {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: rgba(255, 149, 0, 0.1);
    border-radius: var(--radius-sm);
    border-left: 3px solid var(--secondary-color);
}

/* 智能回复强调样式 */
.main-response .response-content {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-primary);
}

.main-response .response-content strong,
.main-response .response-content b {
    color: var(--primary-color);
    font-weight: 600;
}

/* 改善思维过程的可读性 */
.thinking-section .section-content {
    max-height: 400px;
    overflow-y: auto;
}

.thinking-section.collapsed .section-content {
    max-height: 0;
    overflow: hidden;
}

/* 规划步骤的状态指示 */
.planning-step .step-status.status-completed {
    color: var(--success-color);
    font-weight: 600;
}

.planning-step .step-status.status-in_progress {
    color: var(--warning-color);
    font-weight: 600;
}

.planning-step .step-status.status-pending {
    color: var(--text-secondary);
}

.planning-step .step-status.status-failed {
    color: var(--error-color);
    font-weight: 600;
}
