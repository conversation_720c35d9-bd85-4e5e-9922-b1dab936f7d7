"""
Configuration management for the autonomous agent
"""

import os
import json
from typing import Dict, Any, Optional
from pathlib import Path
from pydantic import BaseModel
from .env_config import get_config as get_env_config


class AgentConfig(BaseModel):
    """Configuration settings for the autonomous agent"""

    # Model settings - 从环境配置获取默认值
    model_name: str = None
    model_base_url: str = None
    use_mock: bool = None

    # Display settings
    show_thinking_process: bool = True  # 启用思考过程显示
    show_detailed_logs: bool = False

    # COT model settings - 与前端保持一致
    cot_models: list = [
        "qwen3:32b",
        "qwen2.5-coder",
        "deepseek-coder",
        "claude",
        "gpt-4",
        "qwq:latest",
        "qwq",
        "deepseek-r1:32b",
        "deepseek-r1:70b",
        "deepseek-r1",
        "o1-preview",
        "o1-mini",
        "o1",
    ]

    # Tool settings
    max_retries: int = 3
    timeout_seconds: float = None

    def __init__(self, **data):
        # 从环境配置获取默认值
        env_config = get_env_config()

        # 设置默认值
        if data.get("model_name") is None:
            data["model_name"] = env_config.model.default_model
        if data.get("model_base_url") is None:
            data["model_base_url"] = env_config.model.ollama_base_url
        if data.get("use_mock") is None:
            data["use_mock"] = env_config.model.use_mock
        if data.get("timeout_seconds") is None:
            data["timeout_seconds"] = float(env_config.model.timeout)

        super().__init__(**data)


class ConfigManager:
    """Manages configuration loading, saving, and runtime updates"""

    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or self._get_default_config_path()
        self._config = self._load_config()

    def _get_default_config_path(self) -> str:
        """Get the default configuration file path"""
        config_dir = Path.home() / ".agent_config"
        config_dir.mkdir(exist_ok=True)
        return str(config_dir / "config.json")

    def _load_config(self) -> AgentConfig:
        """Load configuration from file and environment variables"""
        config_data = {}

        # Load from file if it exists
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, "r", encoding="utf-8") as f:
                    config_data = json.load(f)
            except (json.JSONDecodeError, IOError) as e:
                print(f"⚠️ 配置文件加载失败: {e}")

        # Override with environment variables
        env_overrides = {
            "model_name": os.getenv("AGENT_MODEL_NAME"),
            "model_base_url": os.getenv("AGENT_MODEL_URL"),
            "use_mock": os.getenv("AGENT_USE_MOCK", "").lower() in ("true", "1", "yes"),
            "show_thinking_process": os.getenv("AGENT_SHOW_THINKING", "").lower()
            in ("true", "1", "yes"),
            "show_detailed_logs": os.getenv("AGENT_DETAILED_LOGS", "").lower()
            in ("true", "1", "yes"),
        }

        # Apply non-None environment overrides
        for key, value in env_overrides.items():
            if value is not None and value != "":
                config_data[key] = value

        return AgentConfig(**config_data)

    def save_config(self):
        """Save current configuration to file"""
        try:
            config_dir = Path(self.config_file).parent
            config_dir.mkdir(exist_ok=True)

            with open(self.config_file, "w", encoding="utf-8") as f:
                json.dump(self._config.model_dump(), f, indent=2, ensure_ascii=False)
            print(f"✅ 配置已保存到: {self.config_file}")
        except IOError as e:
            print(f"❌ 配置保存失败: {e}")

    def get_config(self) -> AgentConfig:
        """Get current configuration"""
        return self._config

    def update_setting(self, key: str, value: Any) -> bool:
        """Update a configuration setting"""
        if hasattr(self._config, key):
            setattr(self._config, key, value)
            return True
        return False

    def toggle_thinking_display(self) -> bool:
        """Toggle the thinking process display setting"""
        current = self._config.show_thinking_process
        self._config.show_thinking_process = not current
        return self._config.show_thinking_process

    def is_cot_model(self, model_name: str) -> bool:
        """Check if the given model is a COT (Chain of Thought) model"""
        return any(
            cot_model.lower() in model_name.lower()
            for cot_model in self._config.cot_models
        )

    def should_show_thinking(self, model_name: str) -> bool:
        """Determine if thinking process should be shown for the given model"""
        return self._config.show_thinking_process and self.is_cot_model(model_name)

    def get_display_settings(self) -> Dict[str, Any]:
        """Get current display settings"""
        return {
            "show_thinking_process": self._config.show_thinking_process,
            "show_detailed_logs": self._config.show_detailed_logs,
            "cot_models": self._config.cot_models,
        }


# Global configuration manager instance
_config_manager = None


def get_config_manager(config_path: Optional[str] = None) -> ConfigManager:
    """Get the global configuration manager instance"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager(config_path)
    return _config_manager


def get_config() -> AgentConfig:
    """Get the current configuration"""
    return get_config_manager().get_config()
