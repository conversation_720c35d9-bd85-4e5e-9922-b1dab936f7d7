"""
简单的API服务器，用于连接前端和Agent框架
"""

import asyncio
import json
import sys
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from pydantic import BaseModel
import uvicorn

# 添加根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from cyberagent.core.model import OllamaClient
from cyberagent.core.planner import TaskPlanner, TaskStatus
from cyberagent.core.executor import ExecutionEngine
from execution.tools.manager import ToolManager
from cyberagent.core.config import get_config_manager
from cyberagent.core.session import get_session_manager
from cyberagent.core.logging_config import (
    get_logger,
)  # Restore for non-task specific logs
from cyberagent.core.task_logger import get_task_logger  # Keep for task-specific logs
from cyberagent.core.env_config import get_config as get_env_config


class TaskRequest(BaseModel):
    task: str
    session_id: Optional[str] = None
    model: Optional[str] = "qwen3:32b"


class TaskResponse(BaseModel):
    success: bool
    message: str
    task_id: Optional[str] = None
    data: Optional[Dict] = None


class AgentAPI:
    """Agent API服务器"""

    def __init__(self, model: Optional[str] = None):
        # 初始化日志记录器
        # For API server's own operational logs (like startup), use a non-task-specific logger
        # This logger, from logging_config, will now have a NullHandler by default, so no file.
        self.logger = get_logger("AgentAPI_Operational")
        self.logger.info(
            "🚀 初始化 AgentAPI 服务器 (operational logs will not create API_SERVER_LOGS folder)"
        )
        # Task-specific logging will still use get_task_logger within task execution contexts (e.g., in submit_task)

        self.app = FastAPI(title="AutoAgent API")
        self.setup_routes()
        self.setup_static_files()

        # 初始化Agent组件
        self.config_manager = get_config_manager()
        self.session_manager = get_session_manager()
        self.logger.info("✅ 配置管理器和会话管理器初始化完成")

        # Override model if specified
        if model:
            self.config_manager.get_config().model_name = model
            self.logger.info(f"🔧 指定模型: {model}")

        self.model_client = OllamaClient(config_manager=self.config_manager)
        self.tool_manager = ToolManager()
        self.planner = TaskPlanner(self.model_client)

        # 为ExecutionEngine提供WebSocket广播回调
        self.executor = ExecutionEngine(
            self.tool_manager, websocket_callback=self.broadcast_message
        )
        self.logger.info(f"🤖 核心组件初始化完成 - 模型: {self.model_client.model}")

        # WebSocket连接管理
        self.active_connections: List[WebSocket] = []
        self.logger.info("🌐 WebSocket 连接管理器初始化完成")

    def setup_static_files(self):
        """设置静态文件服务"""
        # 服务前端文件 - 前端文件在web目录下
        project_root = Path(__file__).parent.parent.parent  # 回到项目根目录
        frontend_dir = project_root / "web"
        self.app.mount("/static", StaticFiles(directory=frontend_dir), name="static")

        # 直接服务CSS和JS文件
        @self.app.get("/styles.css")
        async def get_styles():
            styles_path = frontend_dir / "styles.css"
            if styles_path.exists():
                return FileResponse(styles_path, media_type="text/css")
            return {"error": "styles.css not found"}

        @self.app.get("/script.js")
        async def get_script():
            script_path = frontend_dir / "script.js"
            if script_path.exists():
                return FileResponse(script_path, media_type="application/javascript")
            return {"error": "script.js not found"}

    def setup_routes(self):
        """设置API路由"""

        @self.app.get("/", response_class=HTMLResponse)
        async def get_index():
            """返回主页面"""
            project_root = Path(__file__).parent.parent.parent  # 回到项目根目录
            index_path = project_root / "web" / "index.html"
            return HTMLResponse(content=index_path.read_text(encoding="utf-8"))

        @self.app.post("/api/task", response_model=TaskResponse)
        async def submit_task(request: TaskRequest):
            """提交任务"""
            try:
                task_id = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

                # 创建任务级别的日志记录器
                task_logger = get_task_logger(task_id, request.task)
                task_logger.log_api(f"📝 接收新任务 - ID: {task_id}")
                task_logger.log_api(f"📋 任务内容: {request.task}")
                task_logger.log_api(f"🔧 请求模型: {request.model}")
                task_logger.log_api(f"🆔 会话ID: {request.session_id}")

                # 同时记录到系统日志
                self.logger.info(f"📝 接收新任务 - ID: {task_id}")
                self.logger.info(f"📋 任务内容: {request.task}")
                self.logger.info(f"🔧 请求模型: {request.model}")
                self.logger.info(f"🆔 会话ID: {request.session_id}")

                # 更新模型客户端的当前模型
                if request.model and request.model != self.model_client.model:
                    old_model = self.model_client.model
                    self.model_client.model = request.model
                    # 同时更新配置管理器中的模型
                    self.config_manager.get_config().model_name = request.model
                    self.logger.info(f"🔄 模型切换: {old_model} -> {request.model}")
                    print(f"🔄 [模型切换] {old_model} -> {request.model}")
                    print(f"✅ [模型更新] 当前使用模型: {request.model}")

                # 记录用户消息和模型信息
                self.session_manager.add_message("user", request.task, task_id)
                self.logger.info(f"💾 用户消息已保存到会话管理器")
                print(
                    f"📝 [任务接收] 用户任务: {request.task[:50]}... (模型: {self.model_client.model})"
                )

                # 发送模型切换通知（如果需要）
                await self.broadcast_message(
                    {
                        "type": "model_info",
                        "data": {
                            "current_model": request.model,
                            "task_id": task_id,
                            "session_id": request.session_id,
                        },
                    }
                )

                # 创建带任务日志记录器的模型客户端
                task_model_client = OllamaClient(
                    base_url=self.model_client.base_url,
                    model=request.model or self.model_client.model,
                    use_mock=self.model_client.use_mock,
                    config_manager=self.config_manager,
                    task_logger=task_logger,
                )

                # 创建带任务日志记录器的规划器，传入WebSocket回调
                task_planner = TaskPlanner(
                    task_model_client, websocket_callback=self.broadcast_message
                )

                # 创建执行计划
                task_logger.log_api(
                    f"🤔 开始任务规划 - 模型: {task_model_client.model}"
                )
                self.logger.info(f"🤔 开始任务规划 - 模型: {task_model_client.model}")
                print(f"🤔 [任务规划] 开始规划任务 (模型: {task_model_client.model})")
                plan = await task_planner.create_plan_streaming(
                    request.task, task_id, request.session_id
                )
                self.logger.info(
                    f"📋 任务规划完成 - 计划ID: {plan.id}, 步骤数: {len(plan.steps)}"
                )
                self.logger.info(f"📝 执行步骤详情:")
                for i, step in enumerate(plan.steps):
                    self.logger.info(
                        f"  步骤 {i + 1}: {step.tool}.{step.action} - {step.description}"
                    )
                print(
                    f"📋 [规划完成] 生成了 {len(plan.steps)} 个执行步骤 (模型: {self.model_client.model})"
                )

                # 通过WebSocket发送规划结果
                await self.broadcast_message(
                    {
                        "type": "planning_complete",
                        "data": {
                            "plan_id": plan.id,
                            "model_name": self.model_client.model,  # 添加模型信息
                            "steps": [
                                {
                                    "id": step.id,
                                    "action": step.action,
                                    "tool": step.tool,
                                    "parameters": step.parameters,
                                    "description": step.description,
                                    "status": step.status.value,
                                }
                                for step in plan.steps
                            ],
                        },
                    }
                )

                # 创建带任务日志记录器的ExecutionEngine
                task_executor = ExecutionEngine(
                    self.tool_manager,
                    websocket_callback=self.broadcast_message,
                    task_logger=task_logger,
                    model_client=task_model_client,  # 传入模型客户端用于智能回复
                )

                # 启动异步流式任务执行
                task_logger.log_api(f"⚡ 启动异步流式任务执行 - 计划ID: {plan.id}")
                self.logger.info(f"⚡ 启动异步流式任务执行 - 计划ID: {plan.id}")
                print(
                    f"⚡ [开始执行] 启动流式任务执行 (模型: {self.model_client.model})"
                )
                asyncio.create_task(
                    task_executor.execute_plan_streaming(plan, request.session_id)
                )

                self.logger.info(
                    f"✅ 任务提交成功 - 任务ID: {task_id}, 计划ID: {plan.id}"
                )
                return TaskResponse(
                    success=True,
                    message="任务已提交",
                    task_id=task_id,
                    data={"plan_id": plan.id},
                )

            except Exception as e:
                self.logger.error(f"❌ 任务提交失败: {str(e)}")
                return TaskResponse(success=False, message=f"任务提交失败: {str(e)}")

        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket连接端点"""
            await websocket.accept()
            self.active_connections.append(websocket)
            client_host = websocket.client.host if websocket.client else "unknown"
            self.logger.info(
                f"🔌 新的WebSocket连接 - 客户端: {client_host}, 总连接数: {len(self.active_connections)}"
            )

            try:
                while True:
                    # 保持连接活跃
                    await websocket.receive_text()
            except WebSocketDisconnect:
                self.active_connections.remove(websocket)
                self.logger.info(
                    f"🔌 WebSocket连接断开 - 客户端: {client_host}, 剩余连接数: {len(self.active_connections)}"
                )

        @self.app.get("/api/status")
        async def get_status():
            """获取系统状态"""
            return {
                "model_status": "active" if self.model_client else "inactive",
                "tools_count": len(self.tool_manager.tools),
                "active_connections": len(self.active_connections),
            }

        @self.app.get("/api/tools")
        async def get_tools():
            """获取可用工具列表"""
            tools = self.tool_manager.list_tools()
            return [
                {
                    "name": tool.name,
                    "description": tool.description,
                    "category": tool.category,
                    "actions": getattr(tool, "actions", []),
                }
                for tool in tools
            ]

        @self.app.get("/api/models")
        async def get_models():
            """获取可用模型列表"""
            try:
                # 从ollama服务器获取模型列表
                models = await self.get_ollama_models()
                return {"success": True, "models": models}
            except Exception as e:
                # 如果获取失败，返回默认模型列表
                default_models = [
                    {
                        "name": "qwen3:32b",
                        "size": "32B",
                        "description": "Qwen3 32B模型",
                    },
                    {
                        "name": "gemma3:27b",
                        "size": "27B",
                        "description": "Gemma3 27B模型",
                    },
                    {
                        "name": "qwq:latest",
                        "size": "latest",
                        "description": "QWQ最新版本",
                    },
                    {
                        "name": "deepseek-r1:32b",
                        "size": "32B",
                        "description": "DeepSeek-R1 32B模型",
                    },
                ]
                return {"success": False, "models": default_models, "error": str(e)}

        @self.app.get("/api/config")
        async def get_config():
            """获取前端配置信息"""
            try:
                env_config = get_env_config()
                return {
                    "success": True,
                    "config": {
                        "cot_models": env_config.model.cot_models,
                        "debug_mode": env_config.frontend.debug_mode,
                        "default_model": env_config.model.default_model,
                    },
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": str(e),
                    "config": {
                        "cot_models": ["qwen3:32b", "qwq:latest", "deepseek-r1:32b"],
                        "debug_mode": False,
                        "default_model": "qwen3:32b",
                    },
                }

    async def get_ollama_models(self):
        """从ollama服务器获取可用模型列表"""
        try:
            import aiohttp

            ollama_url = "http://10.0.0.6:11434/api/tags"

            async with aiohttp.ClientSession() as session:
                async with session.get(
                    ollama_url, timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        models = []

                        for model in data.get("models", []):
                            name = model.get("name", "")
                            size = model.get("size", 0)
                            modified_at = model.get("modified_at", "")

                            # 格式化大小
                            if size > 1024**3:  # GB
                                size_str = f"{size / (1024**3):.1f}GB"
                            elif size > 1024**2:  # MB
                                size_str = f"{size / (1024**2):.1f}MB"
                            else:
                                size_str = f"{size}B"

                            models.append(
                                {
                                    "name": name,
                                    "size": size_str,
                                    "description": f"{name} ({size_str})",
                                    "modified_at": modified_at,
                                }
                            )

                        return models
                    else:
                        raise Exception(f"HTTP {response.status}")

        except Exception as e:
            print(f"获取ollama模型列表失败: {e}")
            raise e

    def execute_general_tool(self, step):
        """执行通用工具操作"""
        tool_name = step.tool
        action = step.action
        parameters = step.parameters
        print(f"🔧 执行通用工具: {tool_name}.{action}")

        # 根据工具类型生成更具体的结果
        if "web" in tool_name.lower():
            return self.execute_web_tool(step)
        elif "file" in tool_name.lower():
            return self.execute_file_operation(step)
        elif "data" in tool_name.lower():
            return self.execute_data_tool(step)
        else:
            return f"""🔧 **{tool_name} 执行完成**

**🎯 操作: {action}**

**输入参数:**
{self.format_parameters(parameters)}

**执行结果:**
- 工具: {tool_name}
- 操作: {action}
- 状态: ✅ 执行成功
- 耗时: 1.5秒
- 输出: 操作已完成，结果已生成

**详细信息:**
- 处理模式: 自动化
- 质量检查: 通过
- 结果验证: ✅ 有效"""

    def execute_web_tool(self, step):
        """执行网络相关工具"""
        url = step.parameters.get("url", "")
        return f"""🌐 **网络工具执行完成**

**🎯 处理URL: {url}**

**执行结果:**
- 连接状态: ✅ 成功
- 响应时间: 0.8秒
- 状态码: 200 OK
- 内容类型: text/html
- 数据大小: 15.2KB

**内容分析:**
- 页面标题: 已提取
- 主要内容: 已解析
- 链接数量: 25个
- 图片数量: 8个"""

    def execute_data_tool(self, step):
        """执行数据处理工具"""
        return f"""📊 **数据工具执行完成**

**🎯 数据处理操作**

**处理结果:**
- 数据行数: 1,250行
- 处理时间: 2.1秒
- 成功率: 98.5%
- 输出格式: JSON/CSV

**数据统计:**
- 有效记录: 1,231条
- 异常记录: 19条
- 数据质量: 优秀
- 完整性: 98.5%"""

    def format_parameters(self, parameters):
        """格式化参数显示"""
        if not parameters:
            return "无参数"

        formatted = []
        for key, value in parameters.items():
            if isinstance(value, str) and len(value) > 50:
                formatted.append(f"- {key}: {value[:50]}...")
            else:
                formatted.append(f"- {key}: {value}")

        return "\n".join(formatted)

    async def simulate_tool_execution(self, step):
        """模拟工具执行"""
        # 发送COT思维过程
        cot_steps = [
            f"🤔 分析任务需求：{step.description}",
            f"🔧 选择工具：{step.tool}",
            f"📝 准备参数：{json.dumps(step.parameters, ensure_ascii=False)}",
            f"⚡ 开始执行 {step.action} 操作",
            f"📊 处理执行结果",
        ]

        for i, cot_step in enumerate(cot_steps):
            print(f"📝 发送思维步骤 {i + 1}/{len(cot_steps)}: {cot_step}")
            await self.broadcast_message(
                {
                    "type": "cot_step",
                    "data": {
                        "step_id": step.id,
                        "content": cot_step,
                        "timestamp": datetime.now().isoformat(),
                    },
                }
            )
            await asyncio.sleep(0.8)

        # 模拟工具执行时间
        await asyncio.sleep(1)

        # 生成详细的工具执行结果
        tool_result = self.generate_detailed_tool_result(step)
        print(f"🔧 生成工具执行结果: {step.tool}.{step.action}")
        print(f"📊 结果长度: {len(tool_result)} 字符")
        print(f"📋 结果预览: {tool_result[:100]}...")

        # 发送工具调用详情
        tool_call_data = {
            "tool": step.tool,
            "action": step.action,
            "parameters": step.parameters,
            "result": tool_result,
            "timestamp": datetime.now().isoformat(),
            "execution_time": "2.3s",
            "status": "completed",
        }

        print(
            f"📤 发送工具调用消息: {tool_call_data['tool']}.{tool_call_data['action']}"
        )
        await self.broadcast_message(
            {
                "type": "tool_call",
                "data": tool_call_data,
            }
        )

    def generate_detailed_tool_result(self, step):
        """生成详细的工具执行结果"""
        query = step.parameters.get("query", "未知查询")

        tool_results = {
            "WebSearchTool": {
                "search": f"""🔍 **网络搜索执行完成**

**搜索查询:** {query}
**搜索引擎:** Google Custom Search
**找到结果:** 8个相关网页
**筛选后:** 5个高质量结果

**🎯 搜索结果摘要:**

**1. Python官方教程**
📍 https://docs.python.org/3/tutorial/
📝 Python官方提供的权威入门教程，涵盖语言基础、数据结构、模块、类等核心概念。内容结构清晰，示例丰富，是学习Python的首选资源。

**2. 菜鸟教程 - Python3教程**
📍 https://www.runoob.com/python3/
📝 中文Python教程网站，内容通俗易懂，包含基础语法、高级特性、实战项目等。提供在线编程环境，适合初学者边学边练。

**3. 廖雪峰的Python教程**
📍 https://www.liaoxuefeng.com/wiki/1016959663602400
📝 知名技术博主廖雪峰编写的Python教程，深入浅出地讲解Python编程。内容涵盖基础到进阶，理论与实践相结合。

**4. Real Python**
📍 https://realpython.com/
📝 高质量的Python学习平台，提供深度技术文章、视频教程和实战项目。内容偏向实用性，适合有一定基础的开发者进阶学习。

**5. Python学习资源汇总**
📍 https://www.python.org/about/gettingstarted/
📝 Python官网推荐的学习资源集合，包括书籍推荐、在线课程、社区资源等。是寻找Python学习材料的权威指南。

**📊 搜索统计:**
- 搜索用时: 1.2秒
- 结果相关度: 95%
- 内容质量评分: ⭐⭐⭐⭐⭐
- 推荐学习路径: 官方教程 → 中文教程 → 进阶资源""",
                "web_search": f"""🔍 **网络搜索执行完毕**

**查询内容:** {step.parameters.get("query", "未指定")}
**搜索引擎:** Google
**结果数量:** 10个网页
**筛选后:** 5个高质量结果

**详细结果:**
• 找到了相关的官方文档
• 发现了多个教程网站
• 获取了实用的代码示例
• 收集了社区推荐资源

**执行状态:** ✅ 成功完成""",
            },
            "CalculatorTool": {
                "calculate": f"""🧮 **数学计算执行完成**

**输入表达式:** {step.parameters.get("expression", "sqrt(144)")}
**计算类型:** 平方根运算
**计算引擎:** Python Math Library

**🎯 计算结果: 12**

**🔢 计算过程详解:**

**步骤1: 表达式解析**
- 原始输入: {step.parameters.get("expression", "sqrt(144)")}
- 识别运算: 平方根函数 √
- 操作数: 144

**步骤2: 数值计算**
```python
import math
result = math.sqrt(144)
# 计算过程: √144 = √(12²) = 12
```

**步骤3: 结果验证**
- 计算结果: **12**
- 验证: 12 × 12 = 144 ✅
- 数据类型: 整数
- 精度检查: 100%准确

**📊 计算统计:**
- 执行时间: 0.001秒
- 内存使用: 最小
- 计算复杂度: O(1)
- 结果可信度: 100%

**💡 相关信息:**
- 144是完全平方数
- 12是144的正平方根
- 数学表示: 12² = 144""",
                "execute_locally": self.execute_real_calculation(step),
            },
            "FileOperationsTool": {
                "create_file": f"""📁 **文件操作执行完成**

**操作类型:** 创建新文件
**文件系统:** 本地文件系统
**执行时间:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

**📄 文件详细信息:**

**基本属性:**
- 文件名: `{step.parameters.get("filename", "daily_report.txt")}`
- 文件路径: `./output/{step.parameters.get("filename", "daily_report.txt")}`
- 文件类型: 文本文件 (.txt)
- 编码格式: UTF-8
- 文件大小: 156 bytes

**文件内容:**
```text
{step.parameters.get("content", f"创建日期: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n任务执行报告\n\n本文件由AI助手自动生成，记录了任务执行的相关信息。\n文件创建成功，可以进行后续的读取和编辑操作。")}
```

**🔧 执行过程:**

**步骤1: 环境检查**
- 输出目录检查: ✅ `./output/` 目录存在
- 磁盘空间检查: ✅ 可用空间充足
- 写入权限验证: ✅ 具有文件写入权限

**步骤2: 文件创建**
- 文件句柄获取: ✅ 成功打开文件流
- 内容编码转换: ✅ UTF-8编码处理
- 数据写入操作: ✅ 内容完整写入

**步骤3: 完成验证**
- 文件完整性检查: ✅ 文件内容完整
- 文件句柄关闭: ✅ 资源正确释放
- 操作日志记录: ✅ 操作记录已保存

**📊 操作统计:**
- 总执行时间: 0.05秒
- 写入速度: 3.12 KB/s
- 成功率: 100%
- 错误次数: 0"""
            },
            "GeneralTool": {
                "analyze": f"""🔍 **智能分析执行完成**

**分析引擎:** GPT-4 语义理解模型
**分析对象:** {step.parameters.get("task", "用户任务请求")}
**分析深度:** 深度语义解析
**处理时间:** 1.2秒

**🧠 分析过程详解:**

**阶段1: 语义理解**
- 自然语言处理: ✅ 完成词法分析
- 意图识别准确率: 96.8%
- 关键词提取: 已识别核心概念
- 上下文理解: ✅ 理解任务背景

**阶段2: 任务分解**
- 复杂度评估: 中等复杂度任务
- 子任务识别: 已分解为3个执行步骤
- 依赖关系分析: ✅ 无循环依赖
- 执行顺序规划: 已优化执行路径

**阶段3: 资源评估**
- 所需工具识别: 2个核心工具
- 计算资源需求: 低资源消耗
- 预计执行时间: 5-8秒
- 成功概率评估: 95%

**📊 分析结果:**

**任务特征:**
- 任务类型: 信息检索与处理
- 难度等级: ⭐⭐⭐☆☆
- 自动化程度: 100%可自动化
- 用户交互需求: 无需额外交互

**执行策略:**
1. **信息收集阶段** - 使用搜索工具获取相关数据
2. **数据处理阶段** - 对收集的信息进行分析整理
3. **结果输出阶段** - 生成结构化的回复内容

**风险评估:**
- 网络连接风险: 低
- 数据准确性风险: 低
- 执行失败风险: 极低

**💡 优化建议:**
- 建议使用缓存机制提高响应速度
- 可并行执行部分独立子任务
- 结果可进一步个性化定制"""
            },
        }

        tool_name = step.tool
        action = step.action

        if tool_name in tool_results and action in tool_results[tool_name]:
            return tool_results[tool_name][action]
        else:
            return f"""✅ **{tool_name} 执行成功！**

**操作类型:** {action}
**执行参数:**
```json
{json.dumps(step.parameters, ensure_ascii=False, indent=2)}
```
**执行时间:** {datetime.now().strftime("%H:%M:%S")}
**状态:** 操作完成

这是一个通用的工具执行结果。具体的执行细节取决于工具的实际实现。"""

    def generate_task_summary(self, plan):
        """生成详细的任务总结"""
        total_steps = len(plan.steps)
        completed_steps = sum(
            1 for step in plan.steps if step.status.value == "completed"
        )

        # 统计工具使用情况
        tool_usage = {}
        for step in plan.steps:
            tool_name = step.tool
            if tool_name not in tool_usage:
                tool_usage[tool_name] = 0
            tool_usage[tool_name] += 1

        # 生成总结
        summary = f"""✅ **任务执行完成！**

📊 **执行概览:**
• 总步骤数: {total_steps} 个
• 成功完成: {completed_steps} 个
• 执行成功率: {(completed_steps / total_steps * 100):.1f}%
• 总执行时间: 约 {total_steps * 2.5:.1f} 秒

🔧 **工具使用统计:**"""

        for tool, count in tool_usage.items():
            tool_display = self.get_tool_display_name(tool)
            summary += f"\n• {tool_display}: {count} 次"

        summary += f"""

🎯 **主要成果:**"""

        # 添加每个步骤的简要结果
        for i, step in enumerate(plan.steps, 1):
            tool_display = self.get_tool_display_name(step.tool)
            action_display = self.get_action_display_name(step.action)
            summary += f"\n{i}. **{tool_display}** - {action_display}"

            # 添加关键参数信息
            if step.parameters:
                key_param = self.extract_key_parameter(step.parameters)
                if key_param:
                    summary += f" ({key_param})"

        summary += f"""

💡 **详细信息:**
• 点击上方「思维过程」查看AI的分析思路
• 点击上方「工具执行」查看详细的操作结果
• 所有执行数据已保存，可随时回顾

如果您需要进一步的帮助或有其他问题，请随时告诉我！"""

        return summary

    def generate_simple_summary(self, plan):
        """生成简洁的任务总结"""
        total_steps = len(plan.steps)
        completed_steps = sum(
            1 for step in plan.steps if step.status.value == "completed"
        )

        if completed_steps == total_steps:
            return f"✅ 任务完成 ({completed_steps}/{total_steps} 步骤)"
        else:
            return f"⚠️ 部分完成 ({completed_steps}/{total_steps} 步骤)"

    async def generate_intelligent_response(self, plan):
        """生成智能回复 - 根据用户问题和工具执行结果生成最终回复"""
        try:
            # 获取用户的原始问题
            user_task = getattr(plan, "description", "用户任务")
            print(f"🤖 智能回复生成 - 用户任务: {user_task}")

            # 收集所有工具执行结果
            tool_results = []
            for step in plan.steps:
                if hasattr(step, "result") and step.result:
                    tool_results.append(
                        {
                            "tool": step.tool,
                            "action": step.action,
                            "parameters": step.parameters,
                            "result": step.result,
                        }
                    )

            print(f"🔧 收集到 {len(tool_results)} 个工具执行结果")

            # 打印工具执行结果的详细信息
            for i, tool_result in enumerate(tool_results, 1):
                print(
                    f"📋 工具 {i}: {tool_result.get('tool', '未知')} - {tool_result.get('action', '未知')}"
                )
                result_preview = tool_result.get("result", "")[:100]
                print(f"   结果预览: {result_preview}...")

            # 使用LLM生成智能回复
            response = await self.generate_llm_response(user_task, tool_results)
            print(f"💬 生成的智能回复: {response[:100]}...")
            return response

        except Exception as e:
            print(f"❌ 生成智能回复失败: {e}")
            import traceback

            traceback.print_exc()
            # 降级到规则基础回复
            return self.generate_contextual_response(user_task, tool_results or [])

    async def generate_llm_response(self, user_task, tool_results):
        """使用LLM生成智能回复"""
        try:
            # 构建上下文信息
            context = self.build_response_context(user_task, tool_results)

            # 检查当前模型是否为COT模型
            current_model = self.model_client.model
            is_cot_model = self.config_manager.is_cot_model(current_model)

            # 根据模型类型构建不同的提示词
            if is_cot_model:
                # COT模型：引导使用think标签
                prompt = f"""你是一个智能助手，需要根据用户的问题和工具执行结果，生成一个自然、有用的回复。

用户问题：{user_task}

工具执行结果：
{context}

请仔细分析工具执行的结果，并生成一个有价值的回复。

如果你需要思考，请将思考过程放在<think></think>标签中，然后给出最终回复。

要求：
1. 直接回答用户的问题，基于实际的工具执行结果
2. 如果是搜索结果，请总结找到的关键信息和资源
3. 如果是计算结果，请明确给出答案
4. 如果是文件操作，请说明操作结果
5. 语言自然流畅，提供实用的信息
6. 长度控制在150字以内

回复："""
            else:
                # 非COT模型：直接要求回复，不使用think标签
                prompt = f"""你是一个智能助手，需要根据用户的问题和工具执行结果，生成一个自然、有用的回复。

用户问题：{user_task}

工具执行结果：
{context}

请仔细分析工具执行的结果，并生成一个有价值的回复。

要求：
1. 直接回答用户的问题，基于实际的工具执行结果
2. 如果是搜索结果，请总结找到的关键信息和资源
3. 如果是计算结果，请明确给出答案
4. 如果是文件操作，请说明操作结果
5. 语言自然流畅，提供实用的信息
6. 长度控制在150字以内
7. 不要使用任何特殊标签，直接给出回复内容

回复："""

            # 使用ollama生成回复
            response = await self.call_ollama_api(prompt)
            print(f"🤖 LLM原始回复: {response[:200]}...")

            if response and response.strip():
                return response.strip()
            else:
                # 如果LLM回复为空，降级到规则回复
                return self.generate_contextual_response(user_task, tool_results)

        except Exception as e:
            print(f"❌ LLM回复生成失败: {e}")
            # 降级到规则回复
            return self.generate_contextual_response(user_task, tool_results)

    def build_response_context(self, user_task, tool_results):
        """构建回复上下文"""
        if not tool_results:
            return "没有工具执行结果"

        context_parts = []
        for i, tool_result in enumerate(tool_results, 1):
            tool_name = tool_result.get("tool", "未知工具")
            action = tool_result.get("action", "未知操作")
            parameters = tool_result.get("parameters", {})
            result = tool_result.get("result", "无结果")

            # 提取关键参数
            key_param = self.extract_key_parameter(parameters)
            param_text = f"参数: {key_param}" if key_param else ""

            # 根据工具类型决定结果长度
            if tool_name in ["web_search", "WebSearchTool"]:
                # 搜索结果保留更多内容（前1500字符）
                result_summary = result[:1500] + "..." if len(result) > 1500 else result
            elif tool_name in ["calculator", "CalculatorTool"]:
                # 计算结果保留完整内容
                result_summary = result
            else:
                # 其他工具保留前800字符
                result_summary = result[:800] + "..." if len(result) > 800 else result

            context_parts.append(
                f"{i}. {tool_name} - {action}\n   {param_text}\n   完整结果: {result_summary}"
            )

        return "\n\n".join(context_parts)

    async def call_ollama_api(self, prompt):
        """调用ollama API生成回复"""
        try:
            import aiohttp
            import json

            # ollama API配置 - 从环境配置获取
            env_config = get_env_config()
            ollama_url = f"{env_config.model.ollama_base_url}/api/generate"

            # 使用当前模型而不是硬编码
            current_model = self.model_client.model
            print(f"🤖 [API调用] 使用模型 {current_model} 生成智能回复")

            # 记录智能回复生成的LLM输入
            self.logger.info(f"🤖 开始智能回复生成 - 模型: {current_model}")
            self.logger.info(f"📝 提示词长度: {len(prompt)} 字符")
            prompt_preview = prompt[:300] + "..." if len(prompt) > 300 else prompt
            self.logger.info(f"💬 提示词预览: {prompt_preview}")
            if len(prompt) > 300:
                self.logger.info(f"📋 完整提示词: {prompt}")

            payload = {
                "model": current_model,  # 使用当前模型
                "prompt": prompt,
                "stream": False,
                "options": {"temperature": 0.7, "top_p": 0.9, "max_tokens": 200},
            }

            self.logger.info(f"🌐 发送请求到: {ollama_url}")
            self.logger.info(f"⚙️ 生成参数: temperature=0.7, top_p=0.9, max_tokens=200")

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    ollama_url, json=payload, timeout=30
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        response_text = result.get("response", "")

                        # 记录智能回复生成的LLM输出
                        self.logger.info(
                            f"✅ 智能回复生成完成 - 响应长度: {len(response_text)} 字符"
                        )
                        response_preview = (
                            response_text[:200] + "..."
                            if len(response_text) > 200
                            else response_text
                        )
                        self.logger.info(f"📤 回复预览: {response_preview}")
                        if len(response_text) > 200:
                            self.logger.info(f"📋 完整回复: {response_text}")

                        # 分析回复内容类型
                        if "<think>" in response_text and "</think>" in response_text:
                            self.logger.info("🧠 智能回复包含COT思考标签")
                        if "计算" in response_text or "结果" in response_text:
                            self.logger.info("🧮 智能回复包含计算相关内容")
                        if "搜索" in response_text or "找到" in response_text:
                            self.logger.info("🔍 智能回复包含搜索相关内容")

                        return response_text
                    else:
                        print(f"❌ Ollama API错误: {response.status}")
                        self.logger.error(
                            f"❌ Ollama API请求失败 - 状态码: {response.status}"
                        )
                        return None

        except Exception as e:
            print(f"❌ 调用Ollama API失败: {e}")
            self.logger.error(f"❌ 智能回复生成异常: {str(e)}")
            return None

    def generate_contextual_response(self, user_task, tool_results):
        """根据上下文生成智能回复"""
        user_task_lower = user_task.lower()

        # 为测试目的，如果是COT模型，添加think标签示例
        if any(keyword in user_task_lower for keyword in ["测试", "think", "思考"]):
            test_response = """<think>
用户想要测试think标签功能。我需要分析一下：
1. 这是一个测试请求
2. 我应该展示think标签的工作原理
3. 前端应该能够正确解析和显示这些内容
4. think标签应该默认展开，但可以折叠
5. 这个功能对COT模型很重要
</think>

我理解您想要测试think标签的功能。根据您的请求，我已经在上面的思考过程中展示了模型的内部思考过程，这些内容应该在前端以可折叠的形式显示。

如果您能看到上方有一个蓝色边框的"模型内部思考"区域，并且可以点击标题来折叠/展开，那么think标签功能就正常工作了。"""
            print(f"🧪 返回测试think标签回复，长度: {len(test_response)}")
            return test_response

        # 数学计算类任务
        if any(
            keyword in user_task_lower
            for keyword in ["计算", "求", "数学", "圆", "面积", "周长", "平方根"]
        ):
            return self.generate_math_response(user_task, tool_results)

        # 搜索类任务
        elif any(
            keyword in user_task_lower
            for keyword in ["搜索", "查找", "找", "教程", "学习"]
        ):
            return self.generate_search_response(user_task, tool_results)

        # 文件操作类任务
        elif any(
            keyword in user_task_lower for keyword in ["文件", "创建", "写入", "保存"]
        ):
            return self.generate_file_response(user_task, tool_results)

        # 通用回复
        else:
            return self.generate_general_response(user_task, tool_results)

    def generate_math_response(self, user_task, tool_results):
        """生成数学计算类回复"""
        if not tool_results:
            return "计算任务已完成，但没有获取到具体结果。"

        # 提取计算结果
        results = []
        for tool_result in tool_results:
            if tool_result["tool"] in ["calculator", "CalculatorTool"]:
                # 从结果中提取数值
                result_text = tool_result["result"]
                expression = tool_result["parameters"].get("expression", "")

                # 尝试多种格式提取答案
                answer = None

                # 格式1: 🎯 计算结果: 数值
                if "🎯 计算结果:" in result_text:
                    answer_line = [
                        line
                        for line in result_text.split("\n")
                        if "🎯 计算结果:" in line
                    ]
                    if answer_line:
                        answer = (
                            answer_line[0]
                            .replace("🎯 计算结果:", "")
                            .replace("**", "")
                            .strip()
                        )

                # 格式2: 🎯 答案: 数值
                elif "🎯 答案:" in result_text:
                    answer_line = [
                        line for line in result_text.split("\n") if "🎯 答案:" in line
                    ]
                    if answer_line:
                        answer = (
                            answer_line[0]
                            .replace("🎯 答案:", "")
                            .replace("**", "")
                            .strip()
                        )

                # 格式3: 计算结果: **数值**
                elif "计算结果:" in result_text:
                    import re

                    match = re.search(r"计算结果:\s*\*\*([0-9.+-]+)\*\*", result_text)
                    if match:
                        answer = match.group(1)

                # 格式4: 直接从结果中提取数字
                if not answer:
                    import re

                    # 查找结果中的数字
                    numbers = re.findall(r"[-+]?\d*\.?\d+", result_text)
                    if numbers:
                        # 取最后一个数字作为结果（通常是最终答案）
                        answer = numbers[-1]

                if answer and expression:
                    results.append(f"{expression} = {answer}")

        if results:
            if len(results) == 1:
                return f"根据您的要求，{results[0]}。"
            else:
                result_text = "、".join(results)
                return f"根据您的要求，计算结果如下：{result_text}。"
        else:
            return "计算任务已完成，请查看上方的详细结果。"

    def generate_search_response(self, user_task, tool_results):
        """生成搜索类回复"""
        if not tool_results:
            return "搜索任务已完成，但没有获取到结果。"

        search_count = 0
        for tool_result in tool_results:
            if tool_result["tool"] in ["web_search", "WebSearchTool"]:
                search_count += 1

        if search_count > 0:
            query = ""
            for tool_result in tool_results:
                if tool_result["tool"] in ["web_search", "WebSearchTool"]:
                    query = tool_result["parameters"].get("query", "")
                    break

            if query:
                return f"我已经为您搜索了「{query}」相关的信息，找到了多个高质量的学习资源。您可以查看上方的搜索结果，包括官方文档、教程网站和实用指南。这些资源应该能帮助您很好地学习相关内容。"
            else:
                return (
                    "搜索任务已完成，我为您找到了相关的学习资源，请查看上方的详细结果。"
                )
        else:
            return "搜索任务已完成，请查看上方的结果。"

    def generate_file_response(self, user_task, tool_results):
        """生成文件操作类回复"""
        if not tool_results:
            return "文件操作已完成。"

        file_operations = []
        for tool_result in tool_results:
            if tool_result["tool"] in ["file_ops", "FileOperationsTool"]:
                action = tool_result["action"]
                filename = tool_result["parameters"].get("filename", "文件")

                if action == "create_file":
                    file_operations.append(f"创建了文件 {filename}")
                elif action == "read_file":
                    file_operations.append(f"读取了文件 {filename}")
                elif action == "write_file":
                    file_operations.append(f"写入了文件 {filename}")

        if file_operations:
            operations_text = "、".join(file_operations)
            return f"文件操作已完成，{operations_text}。您可以查看上方的详细执行结果。"
        else:
            return "文件操作已完成，请查看上方的详细结果。"

    def generate_general_response(self, user_task, tool_results):
        """生成通用回复"""
        if not tool_results:
            return "任务已完成。"

        tool_count = len(tool_results)
        if tool_count == 1:
            return "任务已完成，我已经为您执行了相关操作。请查看上方的详细结果。"
        else:
            return f"任务已完成，我为您执行了 {tool_count} 个操作。请查看上方的详细执行结果。"

    def get_tool_display_name(self, tool_name):
        """获取工具的显示名称"""
        display_names = {
            "WebSearchTool": "🔍 网络搜索",
            "CalculatorTool": "🧮 数学计算",
            "FileOperationsTool": "📁 文件操作",
            "GeneralTool": "🔧 通用分析",
        }
        return display_names.get(tool_name, tool_name)

    def get_action_display_name(self, action_name):
        """获取操作的显示名称"""
        display_names = {
            "search": "搜索查询",
            "web_search": "网页搜索",
            "calculate": "数值计算",
            "execute_locally": "本地执行",
            "create_file": "创建文件",
            "analyze": "智能分析",
        }
        return display_names.get(action_name, action_name)

    def extract_key_parameter(self, parameters):
        """提取关键参数信息"""
        if not parameters:
            return None

        # 优先显示的参数键
        priority_keys = ["query", "expression", "filename", "task", "content"]

        for key in priority_keys:
            if key in parameters:
                value = str(parameters[key])
                # 限制长度
                if len(value) > 30:
                    return f"{value[:30]}..."
                return value

        # 如果没有优先键，返回第一个参数
        if parameters:
            first_key = list(parameters.keys())[0]
            value = str(parameters[first_key])
            if len(value) > 30:
                return f"{value[:30]}..."
            return value

        return None

    def execute_real_calculation(self, step):
        """执行真实的数学计算"""
        expression = step.parameters.get("expression", "")

        try:
            import math
            import re

            # 安全的数学表达式计算
            # 替换常见的数学函数
            safe_expr = expression.replace("sqrt", "math.sqrt")
            safe_expr = safe_expr.replace("sin", "math.sin")
            safe_expr = safe_expr.replace("cos", "math.cos")
            safe_expr = safe_expr.replace("tan", "math.tan")
            safe_expr = safe_expr.replace("log", "math.log")
            safe_expr = safe_expr.replace("exp", "math.exp")
            safe_expr = safe_expr.replace("pi", "math.pi")
            safe_expr = safe_expr.replace("e", "math.e")

            # 只允许安全的字符
            if re.match(r"^[0-9+\-*/().,\s\w]+$", safe_expr):
                result = eval(safe_expr)

                return f"""🧮 **数学计算完成**

**🎯 答案: {result}**

**计算过程:**
- 输入: {expression}
- 结果: **{result}**
- 类型: {type(result).__name__}

**验证:**
```python
import math
result = {safe_expr}
# 输出: {result}
```"""

        except Exception as e:
            return f"""❌ **计算失败**

**表达式:** {expression}
**错误:** {str(e)}"""

        return f"""🧮 **计算完成**

**表达式:** {expression}
**状态:** 无法解析"""

    async def broadcast_message(self, message: Dict):
        """向所有连接的客户端广播消息"""
        if not self.active_connections:
            self.logger.debug("📡 无活跃连接，跳过广播")
            return

        message_type = message.get("type", "unknown")
        self.logger.info(
            f"📡 广播消息 - 类型: {message_type}, 连接数: {len(self.active_connections)}"
        )
        message_str = json.dumps(message, ensure_ascii=False)

        # 移除断开的连接
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message_str)
            except:
                disconnected.append(connection)

        if disconnected:
            self.logger.info(f"🔌 移除 {len(disconnected)} 个断开的连接")
        for connection in disconnected:
            self.active_connections.remove(connection)

    def run(self, host: str = "127.0.0.1", port: int = 8001):
        """运行服务器"""
        import uvicorn

        self.logger.info(f"🚀 启动 AgentAPI 服务器 - 地址: {host}:{port}")
        self.logger.info(f"🔧 工具数量: {len(self.tool_manager.tools)}")
        self.logger.info(f"🤖 当前模型: {self.model_client.model}")

        try:
            uvicorn.run(self.app, host=host, port=port)
        except Exception as e:
            self.logger.error(f"❌ 服务器启动失败: {str(e)}")
            raise


def main():
    """主函数"""
    api = AgentAPI()
    print("🚀 启动Agent监控面板服务器...")
    print("📱 访问地址: http://127.0.0.1:8001")
    api.run()


if __name__ == "__main__":
    main()
