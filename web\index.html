<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent 智能助手</title>
    <link rel="stylesheet" href="styles.css?v=20250129">
    <link href="https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏 - 会话管理 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-robot"></i>
                    <h2>Agent 助手</h2>
                </div>
                <button class="new-chat-btn" id="new-chat-btn">
                    <i class="fas fa-plus"></i>
                    新对话
                </button>
            </div>

            <div class="sessions-list" id="sessions-list">
                <!-- 会话列表将在这里动态生成 -->
            </div>

            <div class="model-selector">
                <label for="model-select">选择模型:</label>
                <select id="model-select" class="model-select">
                    <option value="qwen3:32b">Qwen3 (32B)</option>
                    <option value="gemma3:27b">Gemma3 (27B)</option>
                    <option value="qwq:latest">QWQ (latest)</option>
                    <option value="deepseek-r1:32b">DeepSeek-R1 (32B)</option>
                </select>
            </div>

            <div class="sidebar-footer">
                <div class="status-indicators">
                    <div class="status-item" id="model-status">
                        <i class="fas fa-brain"></i>
                        <span>模型状态</span>
                        <div class="status-dot connecting"></div>
                        <div class="status-tooltip" id="model-tooltip">
                            连接中...
                        </div>
                    </div>
                    <div class="status-item" id="tools-status">
                        <i class="fas fa-tools"></i>
                        <span>工具状态</span>
                        <div class="status-dot active"></div>
                        <div class="status-tooltip" id="tools-tooltip">
                            工具已就绪
                        </div>
                    </div>
                </div>
            </div>
        </aside>

        <!-- 主要聊天区域 -->
        <main class="chat-container">
            <!-- 聊天头部 -->
            <header class="chat-header">
                <div class="chat-title">
                    <h3 id="current-session-title">新对话</h3>
                    <span class="chat-subtitle" id="current-session-subtitle">开始与AI助手对话</span>
                </div>
                <div class="chat-actions">
                    <button class="action-btn" id="settings-btn" title="设置">
                        <i class="fas fa-cog"></i>
                    </button>
                    <button class="action-btn" id="clear-chat-btn" title="清空对话">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button class="action-btn" id="export-chat-btn" title="导出对话">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </header>

            <!-- 聊天消息区域 -->
            <div class="chat-messages" id="chat-messages">
                <div class="welcome-message">
                    <div class="welcome-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3>欢迎使用 Agent 智能助手</h3>
                    <p>我可以帮您执行各种任务，包括网络搜索、文件操作、数学计算等。请告诉我您需要什么帮助。</p>
                    <div class="example-tasks">
                        <button class="example-task" data-task="搜索Python编程教程并解读">搜索Python教程</button>
                        <button class="example-task" data-task="计算半径为10的圆的周长和面积">数学计算</button>
                        <button class="example-task" data-task="创建一个包含今天日期的文件">创建文件</button>
                        <button class="example-task" data-task="分析当前北京的天气情况">天气分析</button>
                    </div>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="chat-input-container">
                <div class="chat-input-wrapper">
                    <textarea
                        id="chat-input"
                        placeholder="输入您的问题或任务..."
                        rows="1"
                    ></textarea>
                    <button id="send-btn" class="send-btn" disabled>
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
                <div class="input-footer">
                    <span class="input-hint">按 Enter 发送，Shift + Enter 换行</span>
                    <div class="typing-indicator" id="typing-indicator">
                        <span>AI正在思考</span>
                        <div class="typing-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 加载动画 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>正在处理任务...</p>
        </div>
    </div>

    <!-- 通知系统 -->
    <div class="notification-container" id="notification-container">
        <!-- 动态生成的通知将在这里显示 -->
    </div>

    <!-- 设置弹窗 -->
    <div class="settings-overlay" id="settings-overlay">
        <div class="settings-modal">
            <div class="settings-header">
                <h3>系统设置</h3>
                <button class="close-btn" id="close-settings">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="settings-content">
                <div class="settings-section">
                    <h4>Ollama模型配置</h4>
                    <div class="setting-item">
                        <label for="model-name">模型名称</label>
                        <select id="model-name" class="setting-input">
                            <option value="qwen3:32b">qwen3:32b</option>
                            <option value="gemma3:27b">gemma3:27b</option>
                            <option value="qwq:latest">qwq:latest</option>
                            <option value="deepseek-r1:32b">deepseek-r1:32b</option>
                            <option value="llama3:8b">llama3:8b</option>
                            <option value="mistral:7b">mistral:7b</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label for="ollama-url">Ollama服务地址</label>
                        <input type="text" id="ollama-url" class="setting-input" placeholder="http://10.0.0.6:11434" value="http://10.0.0.6:11434">
                    </div>
                    <div class="setting-item">
                        <label for="max-tokens">最大Token数</label>
                        <input type="number" id="max-tokens" class="setting-input" placeholder="4096" value="4096" min="512" max="32768">
                    </div>
                    <div class="setting-item">
                        <label for="temperature">温度参数</label>
                        <input type="range" id="temperature" class="setting-range" min="0" max="2" step="0.1" value="0.7">
                        <span class="range-value">0.7</span>
                    </div>
                </div>

                <div class="settings-section">
                    <h4>工具和MCP服务器配置</h4>
                    <div class="setting-item">
                        <label>内置工具</label>
                        <div class="tool-toggles">
                            <div class="tool-toggle">
                                <input type="checkbox" id="tool-web-search" checked>
                                <label for="tool-web-search">
                                    <div class="tool-info">
                                        <span class="tool-name">网络搜索</span>
                                        <span class="tool-desc">搜索网络信息并提供AI分析</span>
                                    </div>
                                </label>
                            </div>
                            <div class="tool-toggle">
                                <input type="checkbox" id="tool-calculator" checked>
                                <label for="tool-calculator">
                                    <div class="tool-info">
                                        <span class="tool-name">数学计算</span>
                                        <span class="tool-desc">执行数学运算和公式计算</span>
                                    </div>
                                </label>
                            </div>
                            <div class="tool-toggle">
                                <input type="checkbox" id="tool-file-ops" checked>
                                <label for="tool-file-ops">
                                    <div class="tool-info">
                                        <span class="tool-name">文件操作</span>
                                        <span class="tool-desc">创建、读取、修改文件</span>
                                    </div>
                                </label>
                            </div>
                            <div class="tool-toggle">
                                <input type="checkbox" id="tool-web-fetch" checked>
                                <label for="tool-web-fetch">
                                    <div class="tool-info">
                                        <span class="tool-name">网页抓取</span>
                                        <span class="tool-desc">获取网页内容并解析</span>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="setting-item">
                        <label>MCP服务器配置</label>
                        <div class="mcp-servers">
                            <div class="mcp-server-item">
                                <div class="mcp-server-header">
                                    <input type="checkbox" id="mcp-filesystem" checked>
                                    <label for="mcp-filesystem">文件系统服务器</label>
                                    <span class="mcp-status connected">已连接</span>
                                </div>
                                <div class="mcp-server-details">
                                    <div class="mcp-config">
                                        <label>服务器地址:</label>
                                        <input type="text" value="stdio://filesystem-server" readonly>
                                    </div>
                                    <div class="mcp-tools">
                                        <span>提供工具: read_file, write_file, list_directory</span>
                                    </div>
                                </div>
                            </div>

                            <div class="mcp-server-item">
                                <div class="mcp-server-header">
                                    <input type="checkbox" id="mcp-web" checked>
                                    <label for="mcp-web">网络服务器</label>
                                    <span class="mcp-status connected">已连接</span>
                                </div>
                                <div class="mcp-server-details">
                                    <div class="mcp-config">
                                        <label>服务器地址:</label>
                                        <input type="text" value="http://localhost:3001/mcp" readonly>
                                    </div>
                                    <div class="mcp-tools">
                                        <span>提供工具: web_search, fetch_url, parse_html</span>
                                    </div>
                                </div>
                            </div>

                            <div class="mcp-server-item">
                                <div class="mcp-server-header">
                                    <input type="checkbox" id="mcp-custom">
                                    <label for="mcp-custom">自定义服务器</label>
                                    <span class="mcp-status disconnected">未连接</span>
                                </div>
                                <div class="mcp-server-details">
                                    <div class="mcp-config">
                                        <label>服务器地址:</label>
                                        <input type="text" placeholder="http://localhost:3002/mcp">
                                    </div>
                                    <div class="mcp-tools">
                                        <span>提供工具: 待连接后显示</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button class="add-mcp-server">+ 添加MCP服务器</button>
                    </div>
                </div>

                <div class="settings-section">
                    <h4>界面设置</h4>
                    <div class="setting-item">
                        <label for="theme-select">主题</label>
                        <select id="theme-select" class="setting-input">
                            <option value="light">浅色主题</option>
                            <option value="dark">深色主题</option>
                            <option value="auto">跟随系统</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label for="font-size">字体大小</label>
                        <select id="font-size" class="setting-input">
                            <option value="small">小</option>
                            <option value="medium" selected>中</option>
                            <option value="large">大</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="settings-footer">
                <button class="btn-secondary" id="reset-settings">重置默认</button>
                <button class="btn-primary" id="save-settings">保存设置</button>
            </div>
        </div>
    </div>

    <script src="script.js?v=20250129-5"></script>
</body>
</html>
